<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LiinkIT - Complete Salon & Spa Management System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #74385C 0%, #401B1B 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            margin-top: 20px;
            margin-bottom: 20px;
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0,0,0,0.15);
        }
        
        .header {
            text-align: center;
            padding: 30px 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0;
            margin: -20px -20px 30px -20px;
        }
        
        .logo {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .subtitle {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        h1 {
            color: #667eea;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
            margin: 30px 0 20px 0;
            font-size: 2em;
        }
        
        h2 {
            color: #764ba2;
            margin: 25px 0 15px 0;
            font-size: 1.5em;
            border-left: 4px solid #764ba2;
            padding-left: 15px;
        }
        
        h3 {
            color: #667eea;
            margin: 20px 0 10px 0;
            font-size: 1.3em;
        }
        
        .section {
            margin: 30px 0;
            padding: 25px;
            background: #f8f9ff;
            border-radius: 10px;
            border: 1px solid #e1e5f2;
        }
        
        .step {
            background: white;
            margin: 15px 0;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        
        .field-list {
            background: #fff;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            border: 1px solid #e1e5f2;
        }
        
        .field-item {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
            color: #555;
        }
        
        .field-item:last-child {
            border-bottom: none;
        }
        
        .highlight {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            text-align: center;
            font-weight: bold;
        }
        
        .menu-item {
            background: white;
            margin: 10px 0;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e1e5f2;
            transition: all 0.3s ease;
        }
        
        .menu-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .table-columns {
            background: #f8f9ff;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        
        .icon {
            font-size: 1.2em;
            margin-right: 8px;
        }
        
        .dashboard-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin: 15px 0;
            border: 1px solid #e1e5f2;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .feature-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #e1e5f2;
            transition: all 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🔗 LiinkIT</div>
            <div class="subtitle">Spa Salon Complete User Guide - From Registration to Full Operations</div>
        </div>

        <h1>🏪 Spa Salon Registration & Complete Journey</h1>

        <div class="section">
            <h2>📝 Registration Process</h2>
            
            <div class="step">
                <h3>🎯 Package Selection</h3>
                <p>Home page banner click leads to "Our Pricing Plan" page with 3 packages:</p>
                <div class="field-list">
                    <div class="field-item">• Free Trial</div>
                    <div class="field-item">• Monthly Package</div>
                    <div class="field-item">• Yearly Package</div>
                </div>
            </div>

            <div class="step">
                <h3>📋 Step 1: Business Information</h3>
                <div class="field-list">
                    <div class="field-item">• Shop Name</div>
                    <div class="field-item">• Email Address</div>
                    <div class="field-item">• Phone Number</div>
                    <div class="field-item">• VAT Number</div>
                    <div class="field-item">• VAT Certification</div>
                    <div class="field-item">• VAT Certification Expiry Date</div>
                    <div class="field-item">• Trade Certification</div>
                    <div class="field-item">• Trade Certification Expiry Date</div>
                    <div class="field-item">• Type Of Centre</div>
                    <div class="field-item">• Password</div>
                    <div class="field-item">• Confirm Password</div>
                </div>
            </div>

            <div class="step">
                <h3>📍 Step 2: Location Information</h3>
                <div class="field-list">
                    <div class="field-item">• Street Address</div>
                    <div class="field-item">• Your City</div>
                    <div class="field-item">• State/Province</div>
                    <div class="field-item">• Postal/Zip Code</div>
                    <div class="field-item">• Country</div>
                    <div class="field-item">• I agree to the Terms and Conditions</div>
                </div>
            </div>

            <div class="highlight">
                ✅ After registration completion, welcome email is sent and user is redirected to dashboard
            </div>
        </div>

        <div class="section">
            <h2>🔒 Initial Dashboard State</h2>
            <div class="warning">
                <strong>⚠️ Important:</strong> After registration, all menus are locked except Center Settings. User must complete center settings to unlock full functionality.
            </div>
        </div>

        <div class="section">
            <h2>⚙️ Center Settings (Required First Step)</h2>
            
            <div class="step">
                <h3>📊 Information Section</h3>
                <div class="field-list">
                    <div class="field-item">• Logo Picture</div>
                    <div class="field-item">• Shop Name</div>
                    <div class="field-item">• Email Address</div>
                    <div class="field-item">• Password</div>
                    <div class="field-item">• Profile Picture</div>
                    <div class="field-item">• Address</div>
                    <div class="field-item">• Location LIINK View on Google Maps</div>
                    <div class="field-item">• City</div>
                    <div class="field-item">• State</div>
                    <div class="field-item">• Snapchat</div>
                    <div class="field-item">• Instagram</div>
                    <div class="field-item">• X (Twitter)</div>
                    <div class="field-item">• WhatsApp</div>
                    <div class="field-item">• Contact Number</div>
                    <div class="field-item">• VAT Number</div>
                    <div class="field-item">• Center Type</div>
                    <div class="field-item">• Appointment Type</div>
                </div>
            </div>

            <div class="step">
                <h3>🕒 Operating Hours</h3>
                <div class="field-list">
                    <div class="field-item">• Opening Time</div>
                    <div class="field-item">• Closing Time</div>
                    <div class="field-item">• Slot Time (5 minutes automatically added for cleaning)</div>
                    <div class="field-item">• Date</div>
                </div>
            </div>

            <div class="step">
                <h3>👥 Team & Facility Description</h3>
                <div class="field-list">
                    <div class="field-item">• Our Team Description</div>
                    <div class="field-item">• Reception Area Picture</div>
                    <div class="field-item">• Reception Area Description</div>
                    <div class="field-item">• Service Area Picture</div>
                    <div class="field-item">• Service Area Description</div>
                </div>
            </div>

            <div class="success">
                ✅ After submitting center settings, all menus unlock and Categories & Services menu becomes available
            </div>
        </div>

        <div class="section">
            <h2>🛍️ Categories and Services Management</h2>

            <div class="step">
                <h3>📋 Services Table View</h3>
                <div class="table-columns">
                    <strong>Table Columns:</strong>
                    <div class="field-item">• S.No.</div>
                    <div class="field-item">• Picture</div>
                    <div class="field-item">• Name</div>
                    <div class="field-item">• Center</div>
                    <div class="field-item">• Category</div>
                    <div class="field-item">• Price</div>
                    <div class="field-item">• Description</div>
                    <div class="field-item">• Actions</div>
                </div>
            </div>

            <div class="step">
                <h3>➕ Add Service Form</h3>
                <div class="field-list">
                    <div class="field-item">• Branch</div>
                    <div class="field-item">• Category</div>
                    <div class="field-item">• Service Name</div>
                    <div class="field-item">• Service Price</div>
                    <div class="field-item">• Service Description</div>
                    <div class="field-item">• Service Picture</div>
                    <div class="field-item">• Employee</div>
                </div>
            </div>

            <div class="highlight">
                🔓 After adding services, Employees menu unlocks
            </div>
        </div>

        <div class="section">
            <h2>👨‍💼 Employee Management</h2>

            <div class="step">
                <h3>📊 Employee Table View</h3>
                <div class="table-columns">
                    <strong>Table Columns:</strong>
                    <div class="field-item">• Actions</div>
                    <div class="field-item">• Image</div>
                    <div class="field-item">• Shop</div>
                    <div class="field-item">• Name</div>
                    <div class="field-item">• Email</div>
                    <div class="field-item">• Phone</div>
                    <div class="field-item">• Contract Expiry Date</div>
                    <div class="field-item">• Services</div>
                    <div class="field-item">• Revenue</div>
                    <div class="field-item">• Appointments</div>
                </div>
            </div>

            <div class="step">
                <h3>➕ Add Employee Form</h3>
                <div class="field-list">
                    <div class="field-item">• Branch</div>
                    <div class="field-item">• Type</div>
                    <div class="field-item">• First Name</div>
                    <div class="field-item">• Last Name</div>
                    <div class="field-item">• Email</div>
                    <div class="field-item">• Password</div>
                    <div class="field-item">• Address</div>
                    <div class="field-item">• Phone</div>
                    <div class="field-item">• Date of Birth</div>
                    <div class="field-item">• Contract Expiry Date</div>
                    <div class="field-item">• Gender</div>
                    <div class="field-item">• Service Category</div>
                    <div class="field-item">• Services</div>
                    <div class="field-item">• Employee ID Attachment</div>
                    <div class="field-item">• Attachment Insurance</div>
                    <div class="field-item">• Attachments The Healthy Card</div>
                    <div class="field-item">• Profile Picture</div>
                </div>
            </div>

            <div class="step">
                <h3>⚙️ Employee Actions</h3>
                <div class="menu-item">
                    <strong>Available Actions:</strong>
                    <div class="field-item">• Edit</div>
                    <div class="field-item">• Last Appointment</div>
                    <div class="field-item">• Consume Products</div>
                    <div class="field-item">• Last Leaves</div>
                    <div class="field-item">• Leave Management</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>💰 Cashier Management</h2>

            <div class="step">
                <h3>📊 Cashier Table View</h3>
                <div class="table-columns">
                    <strong>Table Columns:</strong>
                    <div class="field-item">• Actions</div>
                    <div class="field-item">• Image</div>
                    <div class="field-item">• Center</div>
                    <div class="field-item">• Name</div>
                    <div class="field-item">• Email</div>
                    <div class="field-item">• Address</div>
                    <div class="field-item">• Phone</div>
                    <div class="field-item">• Date of Birth</div>
                    <div class="field-item">• Gender</div>
                </div>
            </div>

            <div class="step">
                <h3>➕ Add New Cashier Form</h3>
                <div class="field-list">
                    <div class="field-item">• Branch</div>
                    <div class="field-item">• First Name</div>
                    <div class="field-item">• Last Name</div>
                    <div class="field-item">• Email</div>
                    <div class="field-item">• Password</div>
                    <div class="field-item">• Phone Number</div>
                    <div class="field-item">• Address</div>
                    <div class="field-item">• Date of Birth</div>
                    <div class="field-item">• Gender</div>
                    <div class="field-item">• Profile Picture</div>
                </div>
            </div>

            <div class="step">
                <h3>⚙️ Cashier Actions</h3>
                <div class="menu-item">
                    <strong>Available Actions:</strong>
                    <div class="field-item">• View</div>
                    <div class="field-item">• Edit</div>
                    <div class="field-item">• Delete</div>
                </div>
            </div>

            <div class="highlight">
                🔓 After adding cashiers, all remaining menus unlock
            </div>
        </div>

        <div class="section">
            <h2>🏨 Amenities Management</h2>

            <div class="step">
                <h3>📊 Amenities Table View</h3>
                <div class="table-columns">
                    <strong>Table Columns:</strong>
                    <div class="field-item">• Picture</div>
                    <div class="field-item">• Amenity</div>
                    <div class="field-item">• Actions</div>
                </div>
            </div>

            <div class="step">
                <h3>➕ Add Amenity</h3>
                <p>Simple amenity selection from predefined list with Add Amenity button</p>
                <div class="menu-item">
                    <strong>Actions Available:</strong>
                    <div class="field-item">• Delete</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>📊 Business Dashboard</h2>

            <div class="step">
                <h3>📈 Dashboard Overview Cards</h3>
                <div class="feature-grid">
                    <div class="dashboard-card">
                        <h4>📅 Active Appointments</h4>
                        <p>Current active appointments count</p>
                    </div>
                    <div class="dashboard-card">
                        <h4>⏰ Upcoming Appointments</h4>
                        <p>Scheduled future appointments</p>
                    </div>
                    <div class="dashboard-card">
                        <h4>📊 Total Appointments</h4>
                        <p>Overall appointment statistics</p>
                    </div>
                    <div class="dashboard-card">
                        <h4>👥 All Customers</h4>
                        <p>Total customer count</p>
                    </div>
                    <div class="dashboard-card">
                        <h4>🛍️ All Services</h4>
                        <p>Total services offered</p>
                    </div>
                    <div class="dashboard-card">
                        <h4>📦 All Products</h4>
                        <p>Total products available</p>
                    </div>
                    <div class="dashboard-card">
                        <h4>💰 Total Revenue</h4>
                        <p>Overall revenue generated</p>
                    </div>
                </div>
            </div>

            <div class="step">
                <h3>🛍️ Services That We Offer</h3>
                <p>Complete list of all services offered by the salon</p>
            </div>
        </div>

        <div class="section">
            <h2>📅 Appointments Management</h2>

            <div class="step">
                <h3>📋 Appointments Calendar</h3>
                <p>Calendar view showing all appointments with date-wise organization</p>
            </div>

            <div class="step">
                <h3>👨‍💼 Assigned Employee Table</h3>
                <div class="table-columns">
                    <strong>Table Columns:</strong>
                    <div class="field-item">• Employee Name</div>
                    <div class="field-item">• Assigned Clients</div>
                    <div class="field-item">• Revenue</div>
                    <div class="field-item">• Action</div>
                </div>
                <div class="menu-item">
                    <strong>Actions Available:</strong>
                    <div class="field-item">• View (shows all assigned clients)</div>
                </div>
            </div>

            <div class="step">
                <h3>📊 Appointments Table</h3>
                <div class="table-columns">
                    <strong>Table Columns:</strong>
                    <div class="field-item">• Name</div>
                    <div class="field-item">• Email</div>
                    <div class="field-item">• Address</div>
                    <div class="field-item">• Phone</div>
                    <div class="field-item">• Time</div>
                    <div class="field-item">• Date</div>
                    <div class="field-item">• Status</div>
                    <div class="field-item">• Client Type</div>
                    <div class="field-item">• Action</div>
                </div>
                <div class="menu-item">
                    <strong>Actions Available:</strong>
                    <div class="field-item">• View (appointment details)</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🏢 Branches Management</h2>

            <div class="step">
                <h3>🏪 Current Branch</h3>
                <p>Display of current branch information and settings</p>
            </div>

            <div class="step">
                <h3>💳 Subscription Management</h3>
                <div class="table-columns">
                    <strong>Subscription Table Columns:</strong>
                    <div class="field-item">• Price</div>
                    <div class="field-item">• Package Type</div>
                    <div class="field-item">• Center Type</div>
                    <div class="field-item">• Payment Date</div>
                    <div class="field-item">• Next Payment Date</div>
                    <div class="field-item">• Subscription Type</div>
                    <div class="field-item">• Invoice</div>
                </div>
                <div class="menu-item">
                    <strong>Available Actions:</strong>
                    <div class="field-item">• Update Subscription (change subscription plan)</div>
                </div>
            </div>

            <div class="step">
                <h3>➕ Add Branch</h3>
                <p>Form to add new branch with package selection:</p>
                <div class="field-list">
                    <div class="field-item">• Monthly Package</div>
                    <div class="field-item">• Yearly Package</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>📦 Products Management</h2>

            <div class="step">
                <h3>📊 Products Table View</h3>
                <div class="table-columns">
                    <strong>Table Columns:</strong>
                    <div class="field-item">• Actions</div>
                    <div class="field-item">• Image</div>
                    <div class="field-item">• Product Name</div>
                    <div class="field-item">• Category</div>
                    <div class="field-item">• Supplier</div>
                    <div class="field-item">• Quantity</div>
                    <div class="field-item">• Current Sale Price</div>
                    <div class="field-item">• Expiry Date</div>
                    <div class="field-item">• Status</div>
                </div>
            </div>

            <div class="step">
                <h3>➕ Add Product Form</h3>
                <div class="field-list">
                    <div class="field-item">• Product Name</div>
                    <div class="field-item">• Branch</div>
                    <div class="field-item">• Brand</div>
                    <div class="field-item">• Product Category</div>
                    <div class="field-item">• Product Type</div>
                    <div class="field-item">• Supplier</div>
                    <div class="field-item">• Description</div>
                    <div class="field-item">• Product Images</div>
                    <div class="field-item">• Quantity</div>
                    <div class="field-item">• Total Cost Without VAT</div>
                    <div class="field-item">• VAT %</div>
                    <div class="field-item">• Total Cost With VAT</div>
                    <div class="field-item">• Sale Price Per Product</div>
                    <div class="field-item">• Per Cost Price</div>
                    <div class="field-item">• SKU ID</div>
                    <div class="field-item">• Expiry Date</div>
                    <div class="field-item">• Shelf</div>
                    <div class="field-item">• Attachment</div>
                </div>
            </div>

            <div class="step">
                <h3>⚙️ Product Actions</h3>
                <div class="menu-item">
                    <strong>Available Actions:</strong>
                    <div class="field-item">• View</div>
                    <div class="field-item">• Edit</div>
                    <div class="field-item">• Delete</div>
                    <div class="field-item">• Stock Management</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>📈 Stock Management</h2>

            <div class="step">
                <h3>📥 Stock In Management (Complete Form)</h3>
                <div class="field-list">
                    <div class="field-item">• SKU ID</div>
                    <div class="field-item">• Quantity</div>
                    <div class="field-item">• Total Cost Without VAT</div>
                    <div class="field-item">• VAT %</div>
                    <div class="field-item">• Total Cost With VAT</div>
                    <div class="field-item">• Sale Price (for sellable products)</div>
                    <div class="field-item">• Per Cost Price</div>
                    <div class="field-item">• Depreciation Date (for equipment)</div>
                    <div class="field-item">• Expiry Date</div>
                    <div class="field-item">• Supplier Selection</div>
                    <div class="field-item">• Shelf Location</div>
                    <div class="field-item">• Total Consume Quantity</div>
                    <div class="field-item">• Total Available Quantity</div>
                    <div class="field-item">• Attachment</div>
                </div>
            </div>

            <div class="step">
                <h3>📤 Stock Out Management (3 Types)</h3>

                <div class="menu-item">
                    <h4>💰 Type 1: Revenue Stock Out</h4>
                    <div class="field-list">
                        <div class="field-item">• Product Selection</div>
                        <div class="field-item">• SKU ID</div>
                        <div class="field-item">• Employee</div>
                        <div class="field-item">• Client Name</div>
                        <div class="field-item">• Quantity</div>
                        <div class="field-item">• Per Product Price</div>
                        <div class="field-item">• Total</div>
                        <div class="field-item">• Date</div>
                        <div class="field-item">• Notes</div>
                    </div>
                </div>

                <div class="menu-item">
                    <h4>💸 Type 2: Expense Stock Out</h4>
                    <div class="field-list">
                        <div class="field-item">• Employee Name</div>
                        <div class="field-item">• Product Selection</div>
                        <div class="field-item">• SKU ID</div>
                        <div class="field-item">• Quantity</div>
                        <div class="field-item">• Cost Price</div>
                        <div class="field-item">• Total</div>
                        <div class="field-item">• Date</div>
                        <div class="field-item">• Select Product</div>
                        <div class="field-item">• Per Product Price</div>
                        <div class="field-item">• Overall Price (With VAT)</div>
                        <div class="field-item">• Notes</div>
                    </div>
                </div>

                <div class="menu-item">
                    <h4>📊 Type 3: Stats/Retain Stock Out</h4>
                    <div class="field-list">
                        <div class="field-item">• Employee Selection</div>
                        <div class="field-item">• Product Selection</div>
                        <div class="field-item">• SKU ID</div>
                        <div class="field-item">• Quantity</div>
                        <div class="field-item">• Per Product Price</div>
                        <div class="field-item">• Total</div>
                        <div class="field-item">• Overall Price</div>
                        <div class="field-item">• VAT %</div>
                        <div class="field-item">• Overall Price (With VAT)</div>
                        <div class="field-item">• Notes</div>
                    </div>
                </div>
            </div>

            <div class="step">
                <h3>📊 Stock Out Table View</h3>
                <div class="table-columns">
                    <strong>Table Columns:</strong>
                    <div class="field-item">• Actions</div>
                    <div class="field-item">• Client Name</div>
                    <div class="field-item">• Client Type</div>
                    <div class="field-item">• No Of Items</div>
                    <div class="field-item">• Type</div>
                    <div class="field-item">• Total Sales</div>
                    <div class="field-item">• Invoice</div>
                </div>
            </div>

            <div class="step">
                <h3>📈 Stock Out Stats Dashboard</h3>
                <div class="feature-grid">
                    <div class="dashboard-card">
                        <h4>📥 Stock Ins</h4>
                        <p>Total stock in quantity</p>
                    </div>
                    <div class="dashboard-card">
                        <h4>📤 Stock Out</h4>
                        <p>Total stock out quantity</p>
                    </div>
                    <div class="dashboard-card">
                        <h4>🏪 Suppliers</h4>
                        <p>Total number of suppliers</p>
                    </div>
                    <div class="dashboard-card">
                        <h4>💰 Net Profit</h4>
                        <p>Revenue minus cost calculation</p>
                    </div>
                    <div class="dashboard-card">
                        <h4>📊 Net Margin</h4>
                        <p>Profit margin percentage</p>
                    </div>
                </div>
                <div class="field-list">
                    <div class="field-item">• Date Range Filter</div>
                    <div class="field-item">• Real-time Stats Updates</div>
                    <div class="field-item">• Revenue vs Cost Analysis</div>
                </div>
            </div>

            <div class="step">
                <h3>📊 Stock Entries Management</h3>
                <p>Update stock levels and manage inventory thresholds</p>
                <div class="field-list">
                    <div class="field-item">• Out of Stock Threshold Setting</div>
                    <div class="field-item">• Overall Availability Quantity Display</div>
                    <div class="field-item">• Total Cost Calculation</div>
                    <div class="field-item">• Stock Warning Alerts</div>
                    <div class="field-item">• Expiry Date Monitoring</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>📞 Help Center</h2>

            <div class="step">
                <h3>📋 Help Center Table View</h3>
                <div class="table-columns">
                    <strong>Table Columns:</strong>
                    <div class="field-item">• #</div>
                    <div class="field-item">• Title</div>
                    <div class="field-item">• Actions</div>
                </div>
            </div>

            <div class="step">
                <h3>👁️ Help Center Functionality</h3>
                <div class="warning">
                    <strong>⚠️ Note:</strong> Spa salon users can only view help tutorials. They cannot add, edit, or delete tutorials.
                </div>
                <div class="menu-item">
                    <strong>Available Actions for Spa Salon:</strong>
                    <div class="field-item">• View Tutorial (opens video link)</div>
                    <div class="field-item">• Access help documentation</div>
                    <div class="field-item">• Browse available tutorials</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>⚙️ Owner Settings</h2>

            <div class="step">
                <h3>🔒 Owner Settings Status</h3>
                <div class="warning">
                    <strong>⚠️ Important:</strong> Owner Settings menu is visible but LOCKED for spa_salon users. This functionality is not accessible.
                </div>
                <div class="menu-item">
                    <strong>Menu Status:</strong>
                    <div class="field-item">• Owner Settings - 🔒 Locked (Not Accessible)</div>
                    <div class="field-item">• Center Settings - ✅ Available (Main settings page)</div>
                </div>
            </div>

            <div class="step">
                <h3>✅ Available Settings</h3>
                <p>Spa salon users manage their settings through <strong>Center Settings</strong> page, not through Owner Settings.</p>
                <div class="success">
                    All salon configuration is done through the Center Settings page which was documented earlier in this guide.
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🔔 Notifications & Alerts</h2>

            <div class="step">
                <h3>📬 System Notifications</h3>
                <div class="menu-item">
                    <strong>Notification Types:</strong>
                    <div class="field-item">• Appointment notifications</div>
                    <div class="field-item">• Payment reminders</div>
                    <div class="field-item">• Stock alerts</div>
                    <div class="field-item">• System updates</div>
                    <div class="field-item">• Employee notifications</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>📋 Complete Menu Structure</h2>

            <div class="feature-grid">
                <div class="feature-card">
                    <h4>🏪 Business Management</h4>
                    <div class="field-item">• Business Dashboard</div>
                    <div class="field-item">• Center Settings</div>
                    <div class="field-item">• Branches Management</div>
                </div>

                <div class="feature-card">
                    <h4>👥 Staff Management</h4>
                    <div class="field-item">• Employee Management</div>
                    <div class="field-item">• Cashier Management</div>
                    <div class="field-item">• Staff Scheduling</div>
                </div>

                <div class="feature-card">
                    <h4>🛍️ Services & Products</h4>
                    <div class="field-item">• Categories & Services</div>
                    <div class="field-item">• Product Management</div>
                    <div class="field-item">• Stock Management</div>
                </div>

                <div class="feature-card">
                    <h4>📅 Operations</h4>
                    <div class="field-item">• Appointments Management</div>
                    <div class="field-item">• Customer Management</div>
                    <div class="field-item">• Revenue Tracking</div>
                </div>

                <div class="feature-card">
                    <h4>🏨 Facilities</h4>
                    <div class="field-item">• Amenities Management</div>
                    <div class="field-item">• Facility Settings</div>
                </div>

                <div class="feature-card">
                    <h4>💳 Financial</h4>
                    <div class="field-item">• Subscription Management</div>
                    <div class="field-item">• Payment Tracking</div>
                    <div class="field-item">• Revenue Reports</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🔄 User Journey Summary</h2>

            <div class="step">
                <h3>📝 Registration Flow</h3>
                <p><strong>Home Page → Package Selection → Registration (2 Steps) → Welcome Email → Dashboard</strong></p>
            </div>

            <div class="step">
                <h3>🔓 Menu Unlock Sequence</h3>
                <div class="field-list">
                    <div class="field-item">1. <strong>Initial:</strong> Only Center Settings available</div>
                    <div class="field-item">2. <strong>After Center Settings:</strong> Categories & Services unlocks</div>
                    <div class="field-item">3. <strong>After Adding Services:</strong> Employee Management unlocks</div>
                    <div class="field-item">4. <strong>After Adding Employees:</strong> All remaining menus unlock</div>
                </div>
            </div>

            <div class="step">
                <h3>✅ Complete Access</h3>
                <p>Once all initial setup is complete, salon owners have full access to:</p>
                <div class="field-list">
                    <div class="field-item">• Complete business dashboard with analytics</div>
                    <div class="field-item">• Full staff management capabilities</div>
                    <div class="field-item">• Comprehensive appointment system</div>
                    <div class="field-item">• Multi-branch management</div>
                    <div class="field-item">• Financial and subscription management</div>
                    <div class="field-item">• Customer relationship management</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>⚠️ Important Corrections</h2>

            <div class="step">
                <h3>📤 Stock Out - 3 Types Confirmed</h3>
                <div class="field-list">
                    <div class="field-item">• <strong>Type 1: Revenue/Sales</strong> - For customer sales</div>
                    <div class="field-item">• <strong>Type 2: Expense</strong> - For internal consumption/expenses</div>
                    <div class="field-item">• <strong>Type 3: Stats/Retain</strong> - For statistical tracking</div>
                </div>
            </div>

            <div class="step">
                <h3>📞 Help Center - View Only</h3>
                <div class="warning">
                    <strong>⚠️ Correction:</strong> Spa salon users can only VIEW help tutorials. They cannot add, edit, or delete tutorials.
                </div>
            </div>

            <div class="step">
                <h3>📥 Stock In - Complete Fields</h3>
                <div class="success">
                    ✅ All 14 fields documented including SKU ID, quantities, costs, VAT, supplier, shelf, attachment, etc.
                </div>
            </div>

            <div class="step">
                <h3>🏪 Salon Listing</h3>
                <div class="warning">
                    <strong>⚠️ Note:</strong> Salon listing is for customers to browse salons, not for spa_salon users to manage.
                </div>
            </div>

            <div class="step">
                <h3>⚙️ Owner Settings</h3>
                <div class="warning">
                    <strong>⚠️ Correction:</strong> Spa salon users don't have Account Settings. They have Owner Settings which is LOCKED. All settings are managed through Center Settings.
                </div>
            </div>
        </div>

        <div class="highlight">
            🎯 <strong>Complete Journey:</strong> From registration to full operational salon management system with all features unlocked and accessible - Field by field documentation completed with corrections
        </div>
    </div>
</body>
</html>
